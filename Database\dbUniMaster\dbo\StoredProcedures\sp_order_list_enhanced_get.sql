-- =============================================
-- Stored Procedure: sp_order_list_enhanced_get
-- Description: Get enhanced paginated list of orders with customer and summary information
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_order_list_enhanced_get]
    @userId NVARCHAR(450) = NULL,
    @filter NVARCHAR(100) = NULL,
    @cust_id UNIQUEIDENTIFIER = NULL,
    @ord_status BIT = NULL,
    @ord_status_pay BIT = NULL,
    @from_date DATETIME = NULL,
    @to_date DATETIME = NULL,
    @customer_type INT = NULL,
    @Offset INT = 0,
    @PageSize INT = 10,
    @Total INT = 0 OUT,
    @TotalFiltered INT = 0 OUT
AS
BEGIN TRY
    SET NOCOUNT ON;

    -- Initialize parameters
    SET @Offset = ISNULL(@Offset, 0);
    SET @PageSize = ISNULL(@PageSize, 10);
    SET @Total = ISNULL(@Total, 0);
    SET @filter = ISNULL(@filter, '');

    -- Validate pagination parameters
    IF @PageSize <= 0 SET @PageSize = 10;
    IF @Offset < 0 SET @Offset = 0;

    -- Build WHERE clause conditions
    DECLARE @WhereClause NVARCHAR(MAX) = N'WHERE 1=1';
    
    IF @filter != ''
    BEGIN
        SET @WhereClause = @WhereClause + N' AND (
            dbo.ufn_removeMark(o.ord_name) LIKE N''%'' + dbo.ufn_removeMark(@filter) + N''%'' 
            OR dbo.ufn_removeMark(o.ord_no) LIKE N''%'' + dbo.ufn_removeMark(@filter) + N''%''
            OR dbo.ufn_removeMark(c.full_name) LIKE N''%'' + dbo.ufn_removeMark(@filter) + N''%''
            OR dbo.ufn_removeMark(c.org_name) LIKE N''%'' + dbo.ufn_removeMark(@filter) + N''%''
        )';
    END
    
    IF @cust_id IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND o.cust_id = @cust_id';
        
    IF @ord_status IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND o.ord_status = @ord_status';
        
    IF @ord_status_pay IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND o.ord_status_pay = @ord_status_pay';
        
    IF @from_date IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND o.created >= @from_date';
        
    IF @to_date IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND o.created <= @to_date';
        
    IF @customer_type IS NOT NULL
        SET @WhereClause = @WhereClause + N' AND c.cust_type = @customer_type';

    -- Get total count
    DECLARE @CountSQL NVARCHAR(MAX) = N'
        SELECT @Total = COUNT(*)
        FROM dbo.[order] o
        LEFT JOIN dbo.customer c ON o.cust_id = c.id
        ' + @WhereClause;
    
    EXEC sp_executesql @CountSQL, 
        N'@filter NVARCHAR(100), @cust_id UNIQUEIDENTIFIER, @ord_status BIT, @ord_status_pay BIT, 
          @from_date DATETIME, @to_date DATETIME, @customer_type INT, @Total INT OUTPUT',
        @filter, @cust_id, @ord_status, @ord_status_pay, @from_date, @to_date, @customer_type, @Total OUTPUT;

    SET @TotalFiltered = @Total;

    -- Return grid configuration if offset is 0
    IF @Offset = 0
    BEGIN
        SELECT *
        FROM [dbo].fn_config_list_gets('view_order_enhanced_page', 0)
        ORDER BY [ordinal];
    END;

    -- Get paginated data with enhanced information
    SELECT 
        o.id as OrderId,
        o.ord_name as OrderName,
        o.ord_no as OrderNumber,
        o.voucher_no as VoucherNumber,
        o.jump as JumpStatus,
        
        -- Order status
        o.ord_status as OrderStatus,
        ISNULL(ord_status_config.value1, 
            CASE o.ord_status 
                WHEN 0 THEN N'Chờ xử lý'
                WHEN 1 THEN N'Hoàn thành'
                ELSE N'Không xác định'
            END
        ) as OrderStatusText,
        
        o.ord_status_pay as PaymentStatus,
        ISNULL(pay_status_config.value1,
            CASE o.ord_status_pay 
                WHEN 0 THEN N'Chưa thanh toán'
                WHEN 1 THEN N'Đã thanh toán'
                ELSE N'Không xác định'
            END
        ) as PaymentStatusText,
        
        -- Customer information
        c.id as CustomerId,
        c.code as CustomerCode,
        c.cust_type as CustomerType,
        CASE c.cust_type 
            WHEN 0 THEN N'Cá nhân'
            WHEN 1 THEN N'Doanh nghiệp'
            ELSE N'Không xác định'
        END as CustomerTypeText,
        c.full_name as CustomerName,
        c.org_name as OrganizationName,
        c.phone_1 as CustomerPhone,
        c.email_1 as CustomerEmail,
        
        -- Order summary
        order_summary.total_items,
        order_summary.total_amount,
        order_summary.product_names,
        
        -- Contract count
        ISNULL(contract_count.contract_count, 0) as ContractCount,
        
        -- Timestamps
        o.created as CreatedDate,
        o.created_by as CreatedBy,
        o.updated as UpdatedDate,
        o.updated_by as UpdatedBy
        
    FROM dbo.[order] o
    LEFT JOIN dbo.customer c ON o.cust_id = c.id
    LEFT JOIN dbo.sys_config_data ord_status_config ON ord_status_config.key_1 = 'ord_status' 
        AND o.ord_status = ord_status_config.key_2
    LEFT JOIN dbo.sys_config_data pay_status_config ON pay_status_config.key_1 = 'ord_status_pay' 
        AND o.ord_status_pay = pay_status_config.key_2
    LEFT JOIN (
        -- Order summary subquery
        SELECT 
            od.ord_id,
            COUNT(*) as total_items,
            SUM(ISNULL(od.amount, 0)) as total_amount,
            STRING_AGG(p.prod_name, ', ') as product_names
        FROM dbo.order_details od
        LEFT JOIN dbo.products p ON od.prod_id = p.id
        GROUP BY od.ord_id
    ) order_summary ON o.id = order_summary.ord_id
    LEFT JOIN (
        -- Contract count subquery
        SELECT 
            od.ord_id,
            COUNT(DISTINCT c.id) as contract_count
        FROM dbo.order_details od
        LEFT JOIN dbo.contract c ON c.ord_dts_id = od.id
        GROUP BY od.ord_id
    ) contract_count ON o.id = contract_count.ord_id
    WHERE 1=1
        AND (@filter = '' OR (
            dbo.ufn_removeMark(o.ord_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + N'%' 
            OR dbo.ufn_removeMark(o.ord_no) LIKE N'%' + dbo.ufn_removeMark(@filter) + N'%'
            OR dbo.ufn_removeMark(c.full_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + N'%'
            OR dbo.ufn_removeMark(c.org_name) LIKE N'%' + dbo.ufn_removeMark(@filter) + N'%'
        ))
        AND (@cust_id IS NULL OR o.cust_id = @cust_id)
        AND (@ord_status IS NULL OR o.ord_status = @ord_status)
        AND (@ord_status_pay IS NULL OR o.ord_status_pay = @ord_status_pay)
        AND (@from_date IS NULL OR o.created >= @from_date)
        AND (@to_date IS NULL OR o.created <= @to_date)
        AND (@customer_type IS NULL OR c.cust_type = @customer_type)
    ORDER BY 
        CASE WHEN o.updated > o.created THEN o.updated ELSE o.created END DESC,
        o.ord_name DESC 
    OFFSET @Offset ROWS 
    FETCH NEXT @PageSize ROWS ONLY;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_list_enhanced_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'Filter: ' + ISNULL(@filter, 'NULL') + ', PageSize: ' + CAST(@PageSize AS NVARCHAR(10));

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET_LIST',
                          @SessionID,
                          @AddlInfo;
                          
    -- Re-throw the error
    THROW;
END CATCH;

GO
