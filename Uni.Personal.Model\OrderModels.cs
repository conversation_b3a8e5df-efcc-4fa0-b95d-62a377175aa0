using System;
using System.Collections.Generic;
using UNI.Model;

namespace Uni.Personal.Model
{
    /// <summary>
    /// Filter input for Order pagination
    /// </summary>
    public class OrderFilterInput : FilterInput
    {
        public string? OrderCode { get; set; }
        public string? ContractCode { get; set; }
        public string? CustomerName { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public int? Status { get; set; }
        public string? ProductType { get; set; }
    }

    /// <summary>
    /// Order information model for create/update operations
    /// </summary>
    public class OrderInfo : CommonViewOidInfo
    {
        public string? OrderCode { get; set; }
        public string? ContractCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public string? ProductType { get; set; }
        public decimal? SubTotal { get; set; }
        public decimal? VatRate { get; set; }
        public decimal? VatAmount { get; set; }
        public decimal? TotalAmount { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public List<OrderItemInfo>? OrderItems { get; set; }
    }

    /// <summary>
    /// Order item information
    /// </summary>
    public class OrderItemInfo
    {
        public Guid? Oid { get; set; }
        public Guid? OrderOid { get; set; }
        public int? ItemNo { get; set; }
        public string? ProductType { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int? Quantity { get; set; }
        public string? Unit { get; set; }
        public decimal? UnitPrice { get; set; }
        public decimal? TotalPrice { get; set; }
        public int? Duration { get; set; }
        public string? DurationUnit { get; set; }
    }

    /// <summary>
    /// Order list item for display in grid
    /// </summary>
    public class OrderListItem
    {
        public Guid Oid { get; set; }
        public string? OrderCode { get; set; }
        public string? ContractCode { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? ProductType { get; set; }
        public decimal? TotalAmount { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CreatedBy { get; set; }
    }

    /// <summary>
    /// Order status enumeration
    /// </summary>
    public enum OrderStatus
    {
        Draft = 0,
        WaitingForPayment = 1,
        Paid = 2,
        Processing = 3,
        Completed = 4,
        Cancelled = 5,
        Refunded = 6
    }

    /// <summary>
    /// Order detail view model for displaying complete order information
    /// </summary>
    public class OrderDetailView
    {
        public Guid Oid { get; set; }
        public string? OrderCode { get; set; }
        public string? ContractCode { get; set; }
        public DateTime? OrderDate { get; set; }
        public DateTime? CreatedDate { get; set; }
        public string? CustomerName { get; set; }
        public string? CustomerEmail { get; set; }
        public string? CustomerPhone { get; set; }
        public int? Status { get; set; }
        public string? StatusText { get; set; }
        public decimal? SubTotal { get; set; }
        public decimal? VatRate { get; set; }
        public decimal? VatAmount { get; set; }
        public decimal? TotalAmount { get; set; }
        public string? Description { get; set; }
        public string? Notes { get; set; }
        public List<OrderItemView>? OrderItems { get; set; }
    }

    /// <summary>
    /// Order item view model for displaying order item details
    /// </summary>
    public class OrderItemView
    {
        public int? ItemNo { get; set; }
        public string? ProductType { get; set; }
        public string? ProductName { get; set; }
        public string? ProductDescription { get; set; }
        public int? Quantity { get; set; }
        public string? Unit { get; set; }
        public decimal? UnitPrice { get; set; }
        public decimal? TotalPrice { get; set; }
        public int? Duration { get; set; }
        public string? DurationUnit { get; set; }
    }
}
