-- =============================================
-- Stored Procedure: sp_order_detail_info_get
-- Description: Get detailed order information including customer details, order items, and related data
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_order_detail_info_get]
    @order_id UNIQUEIDENTIFIER,
    @userId NVARCHAR(450) = NULL
AS
BEGIN TRY
    SET NOCOUNT ON;

    -- Validate input parameters
    IF @order_id IS NULL
    BEGIN
        RAISERROR('Order ID is required', 16, 1);
        RETURN;
    END

    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @order_id)
    BEGIN
        RAISERROR('Order not found', 16, 1);
        RETURN;
    END

    -- Get order header information with customer details
    SELECT 
        o.id as OrderId,
        o.ord_name as OrderName,
        o.ord_no as OrderNumber,
        o.voucher_no as VoucherNumber,
        o.jump as JumpStatus,
        
        -- Order status information
        o.ord_status as OrderStatus,
        ord_status_config.value1 as OrderStatusText,
        o.ord_status_pay as PaymentStatus,
        pay_status_config.value1 as PaymentStatusText,
        
        -- Customer information
        c.id as CustomerId,
        c.code as CustomerCode,
        c.cif_no as CifNumber,
        c.cust_type as CustomerType,
        CASE c.cust_type 
            WHEN 0 THEN N'Cá nhân'
            WHEN 1 THEN N'Doanh nghiệp'
            ELSE N'Không xác định'
        END as CustomerTypeText,
        c.full_name as CustomerName,
        c.birth_day as BirthDay,
        c.sex as Gender,
        c.per_address as PersonalAddress,
        c.org_name as OrganizationName,
        c.establish_date as EstablishDate,
        c.tax as TaxCode,
        c.fax as FaxNumber,
        c.postion as Position,
        c.phone_1 as PrimaryPhone,
        c.phone_2 as SecondaryPhone,
        c.email_1 as PrimaryEmail,
        c.email_2 as SecondaryEmail,
        c.org_address as OrganizationAddress,
        
        -- Location information
        p.name as ProvinceName,
        d.name as DistrictName,
        n.name as NationalName,
        
        -- Order timestamps
        o.created as CreatedDate,
        o.created_by as CreatedBy,
        o.updated as UpdatedDate,
        o.updated_by as UpdatedBy,
        
        -- Additional customer info
        c.description as CustomerDescription
        
    FROM dbo.[order] o
    LEFT JOIN dbo.customer c ON o.cust_id = c.id
    LEFT JOIN dbo.province p ON c.provinve_id = p.id
    LEFT JOIN dbo.district d ON c.district_id = d.id
    LEFT JOIN dbo.national n ON c.national_id = n.id
    LEFT JOIN dbo.sys_config_data ord_status_config ON ord_status_config.key_1 = 'ord_status' 
        AND o.ord_status = ord_status_config.key_2
    LEFT JOIN dbo.sys_config_data pay_status_config ON pay_status_config.key_1 = 'ord_status_pay' 
        AND o.ord_status_pay = pay_status_config.key_2
    WHERE o.id = @order_id;

    -- Get order details/items information
    SELECT 
        od.id as OrderDetailId,
        od.ord_id as OrderId,
        od.prod_id as ProductId,
        od.package_id as PackageId,
        od.start_dt as StartDate,
        od.end_dt as EndDate,
        od.type as RegistrationType,
        CASE od.type
            WHEN 1 THEN N'Đăng ký mới'
            WHEN 2 THEN N'Gia hạn'
            WHEN 3 THEN N'Nâng cấp'
            ELSE N'Không xác định'
        END as RegistrationTypeText,
        od.amount as Amount,
        
        -- Product information
        p.prod_name as ProductName,
        p.prod_code as ProductCode,
        p.description as ProductDescription,
        p.price as ProductPrice,
        p.status as ProductStatus,
        
        -- Package information
        pkg.name as PackageName,
        pkg.description as PackageDescription,
        pkg.price as PackagePrice,
        pkg.duration as PackageDuration,
        pkg.duration_type as PackageDurationType,
        
        -- Product category
        pc.name as CategoryName,
        
        -- Timestamps
        od.created as CreatedDate,
        od.created_by as CreatedBy,
        od.updated as UpdatedDate,
        od.updated_by as UpdatedBy
        
    FROM dbo.order_details od
    LEFT JOIN dbo.products p ON od.prod_id = p.id
    LEFT JOIN dbo.product_package pkg ON od.package_id = pkg.id
    LEFT JOIN dbo.product_category pc ON p.category_id = pc.id
    WHERE od.ord_id = @order_id
    ORDER BY od.created ASC;

    -- Get contract information related to this order
    SELECT 
        c.id as ContractId,
        c.contract_no as ContractNumber,
        c.contract_name as ContractName,
        c.start_date as StartDate,
        c.end_date as EndDate,
        c.status as Status,
        c.created as CreatedDate,
        c.created_by as CreatedBy
    FROM dbo.contract c
    INNER JOIN dbo.order_details od ON c.ord_dts_id = od.id
    WHERE od.ord_id = @order_id
    ORDER BY c.created ASC;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_detail_info_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + CAST(@order_id AS NVARCHAR(50));

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET_DETAIL',
                          @SessionID,
                          @AddlInfo;
                          
    -- Re-throw the error
    THROW;
END CATCH;

GO
