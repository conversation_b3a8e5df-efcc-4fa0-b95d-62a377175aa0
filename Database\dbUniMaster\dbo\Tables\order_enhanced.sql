-- =============================================
-- Enhanced Order Table Structure
-- Description: Modified order table to support the order details UI functionality
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

-- Drop existing constraints and table if needed for modification
-- Note: Run this carefully in production - backup data first!

/*
-- Backup existing data
SELECT * INTO order_backup FROM [dbo].[order];

-- Drop foreign key constraints
ALTER TABLE [dbo].[order_details] DROP CONSTRAINT [FK_order_details_order];

-- Drop existing table
DROP TABLE [dbo].[order];
*/

-- Create enhanced order table
CREATE TABLE [dbo].[order] (
    [id]                UNIQUEIDENTIFIER CONSTRAINT [DF_order_id] DEFAULT (newid()) NOT NULL,
    [cust_id]           UNIQUEIDENTIFIER NULL,
    
    -- Order identification
    [ord_name]          NVARCHAR (100)   NULL, -- Increased size for longer order names
    [ord_no]            NVARCHAR (50)    NULL, -- Order number/code
    [contract_code]     NVARCHAR (50)    NULL, -- Contract code as shown in UI
    [voucher_no]        NVARCHAR (50)    NULL,
    
    -- Order status (enhanced to support more statuses)
    [ord_status]        INT              CONSTRAINT [DF_order_status] DEFAULT (0) NULL, 
    -- 0: Draft/Nháp, 1: WaitingForPayment/Chờ thanh toán, 2: Paid/Đã thanh toán, 
    -- 3: Processing/Đang xử lý, 4: Completed/Hoàn thành, 5: Cancelled/Đã hủy, 6: Refunded/Đã hoàn tiền
    
    [ord_status_pay]    INT              CONSTRAINT [DF_order_status_pay] DEFAULT (0) NULL,
    -- 0: Unpaid/Chưa thanh toán, 1: Paid/Đã thanh toán, 2: PartiallyPaid/Thanh toán một phần, 3: Refunded/Đã hoàn tiền
    
    -- Financial information (as shown in UI)
    [sub_total]         DECIMAL(18,2)    CONSTRAINT [DF_order_sub_total] DEFAULT (0) NULL,
    [vat_rate]          DECIMAL(5,2)     CONSTRAINT [DF_order_vat_rate] DEFAULT (10) NULL, -- VAT percentage
    [vat_amount]        DECIMAL(18,2)    CONSTRAINT [DF_order_vat_amount] DEFAULT (0) NULL,
    [total_amount]      DECIMAL(18,2)    CONSTRAINT [DF_order_total_amount] DEFAULT (0) NULL,
    [discount_amount]   DECIMAL(18,2)    CONSTRAINT [DF_order_discount] DEFAULT (0) NULL,
    
    -- Order dates
    [order_date]        DATETIME         CONSTRAINT [DF_order_date] DEFAULT (getdate()) NULL,
    [payment_due_date]  DATETIME         NULL,
    [payment_date]      DATETIME         NULL,
    
    -- Additional information
    [description]       NVARCHAR(MAX)    NULL,
    [notes]            NVARCHAR(MAX)    NULL,
    [payment_method]   NVARCHAR(50)     NULL, -- Credit Card, Bank Transfer, etc.
    [payment_reference] NVARCHAR(100)   NULL, -- Payment transaction reference
    
    -- Legacy field
    [jump]             INT              CONSTRAINT [DF_order_jump] DEFAULT ((1)) NULL,
    
    -- Audit fields
    [created]          DATETIME         CONSTRAINT [DF_order_created] DEFAULT (getdate()) NULL,
    [created_by]       UNIQUEIDENTIFIER NULL,
    [updated]          DATETIME         NULL,
    [updated_by]       UNIQUEIDENTIFIER NULL,
    
    CONSTRAINT [PK_order] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_order_customer] FOREIGN KEY ([cust_id]) REFERENCES [dbo].[customer] ([id])
);

-- Create indexes for better performance
CREATE INDEX [IX_order_ord_no] ON [dbo].[order] ([ord_no]);
CREATE INDEX [IX_order_contract_code] ON [dbo].[order] ([contract_code]);
CREATE INDEX [IX_order_cust_id] ON [dbo].[order] ([cust_id]);
CREATE INDEX [IX_order_status] ON [dbo].[order] ([ord_status]);
CREATE INDEX [IX_order_status_pay] ON [dbo].[order] ([ord_status_pay]);
CREATE INDEX [IX_order_order_date] ON [dbo].[order] ([order_date]);
CREATE INDEX [IX_order_created] ON [dbo].[order] ([created]);

-- Add extended properties for documentation
EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'0: Nháp, 1: Chờ thanh toán, 2: Đã thanh toán, 3: Đang xử lý, 4: Hoàn thành, 5: Đã hủy, 6: Đã hoàn tiền', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'ord_status';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'0: Chưa thanh toán, 1: Đã thanh toán, 2: Thanh toán một phần, 3: Đã hoàn tiền', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'ord_status_pay';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Tổng tiền trước VAT', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'sub_total';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Tỷ lệ VAT (%)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'vat_rate';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Tiền VAT', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'vat_amount';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Tổng tiền thanh toán', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order', 
    @level2type = N'COLUMN', @level2name = N'total_amount';

GO
