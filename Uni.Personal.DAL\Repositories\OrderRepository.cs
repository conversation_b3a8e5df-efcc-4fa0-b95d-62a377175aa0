using System;
using System.Linq;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.DAL.Repositories
{
    /// <summary>
    /// Order repository implementation
    /// </summary>
    public class OrderRepository : UniBaseRepository, IOrderRepository
    {
        public OrderRepository(IUniCommonBaseRepository commonBase) : base(commonBase)
        {
        }

        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="filter">Filter criteria</param>
        /// <returns>Paginated list of orders</returns>
        public async Task<CommonListPage> GetPageAsync(OrderFilterInput filter)
        {
            // Call stored procedure to get paginated orders
            return await GetPageAsync("sp_personal_order_page", filter, param =>
            {
                // Add custom parameters if needed
                // param.Add("@OrderCode", filter.OrderCode);
                // param.Add("@CustomerName", filter.CustomerName);
                // param.Add("@FromDate", filter.FromDate);
                // param.Add("@ToDate", filter.ToDate);
                param.Add("@Status", filter.Status);
                // param.Add("@ProductType", filter.ProductType);
                return param;
            });
        }

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            // Call stored procedure to get order info
            return await GetFirstOrDefaultAsync<CommonViewOidInfo>("sp_personal_order_GetInfo", null, new { Oid = oid });
        }

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo info)
        {
            // Call stored procedure to create/update order
            return await SetAsync<Guid?>("sp_personal_order_SetInfo", info.ToObject());
        }

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Call stored procedure to delete order
            return await SetAsync("sp_personal_order_Delete", new { Oid = oid });
        }

        /// <summary>
        /// Get detailed order information including items
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Detailed order information</returns>
        public async Task<OrderDetailView?> GetOrderDetailAsync(Guid? oid)
        {
            // Call stored procedure to get detailed order information with multiple result sets
            return await GetMultipleAsync<OrderDetailView?>("sp_personal_order_GetDetail", new { Oid = oid },
                async reader =>
                {
                    // Read the order header from the first result set
                    var orderDetail = await reader.ReadFirstOrDefaultAsync<OrderDetailView>();

                    if (orderDetail != null)
                    {
                        // Read the order items from the second result set
                        var orderItems = (await reader.ReadAsync<OrderItemView>()).ToList();
                        orderDetail.OrderItems = orderItems;
                    }

                    return orderDetail;
                });
        }

        /// <summary>
        /// Update order status
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, int status)
        {
            // Call stored procedure to update order status
            return await SetAsync("sp_personal_order_UpdateStatus", new { Oid = oid, Status = status });
        }
    }
}
