using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UNI.Model.Commons;

namespace UNI.Common.CommonBase
{
    public interface IUniCommonBaseRepository
    {
        CommonInfo CommonInfo { get; }

        IConfiguration Configuration { get; }
    }

    public class UniCommonBaseRepository : IUniCommonBaseRepository
    {
        public UniCommonBaseRepository(IServiceProvider serviceProvider, string connectionString = null, string commonFilterStored = null, bool isAcceptLanguage = false)
        {
            _configuration = serviceProvider.GetService<Microsoft.Extensions.Configuration.IConfiguration>();
            var httpContextAccessor = serviceProvider.GetService<Microsoft.AspNetCore.Http.IHttpContextAccessor>();
            if (string.IsNullOrEmpty(connectionString))
            {
                var connectionStrings = _configuration.GetSection("ConnectionStrings")?.GetChildren();
                connectionString = connectionStrings?.FirstOrDefault()?.Key;
            }
            var currUserId = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "sub" || c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier").Select(c1 => c1.Value).FirstOrDefault();
            var acceptLanguage = httpContextAccessor.HttpContext.Request.Headers["Accept-Language"].ToString().Split(";").FirstOrDefault()?.Split(",").FirstOrDefault();
            var clientType = _configuration.GetSection("AppSettings:ClientType").Value;
            Enum.TryParse(clientType, true, out ClientTypes clientTypes);
            _commonInfo = new()
            {
                ClientIp = httpContextAccessor.HttpContext.Connection.RemoteIpAddress?.ToString(),
                hostUrl = httpContextAccessor.HttpContext.Request.Scheme + "://" + httpContextAccessor.HttpContext.Request.Host.Value,
                ClientId = httpContextAccessor.HttpContext.User.Claims.Where(c => c.Type == "client_id" || c.Type == "azp").Select(c1 => c1.Value).FirstOrDefault(),
                UserId = currUserId,
                ConnectionString = _configuration.GetConnectionString(connectionString),
                IsAcceptLanguage = isAcceptLanguage,
                AcceptLanguage = acceptLanguage,
                ClientType = clientTypes,
                CommonFilterStored = commonFilterStored
            };
        }

        private CommonInfo _commonInfo;
        public CommonInfo CommonInfo { get => _commonInfo; }

        private IConfiguration _configuration;
        public IConfiguration Configuration { get => _configuration; }
    }
}