-- =============================================
-- Stored Procedure: sp_personal_order_detail_get
-- Description: Get detailed order information including customer details and order items
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_personal_order_detail_get]
    @order_id UNIQUEIDENTIFIER
AS
BEGIN TRY
    SET NOCOUNT ON;

    -- Validate input parameters
    IF @order_id IS NULL
    BEGIN
        RAISERROR('Order ID is required', 16, 1);
        RETURN;
    END

    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @order_id)
    BEGIN
        RAISERROR('Order not found', 16, 1);
        RETURN;
    END

    -- Return order header information with customer details
    SELECT 
        o.id as Oid,
        o.ord_name as OrderCode,
        o.ord_no as ContractCode,
        o.created as OrderDate,
        o.created as CreatedDate,
        
        -- Customer information
        c.full_name as CustomerName,
        c.email_1 as CustomerEmail,
        c.phone_1 as CustomerPhone,
        
        -- Order status
        o.ord_status as Status,
        CASE o.ord_status
            WHEN 0 THEN N'Chờ thanh toán'
            WHEN 1 THEN N'Hoàn thành'
            ELSE N'Không xác định'
        END as StatusText,
        
        -- Calculate totals from order details
        ISNULL(order_totals.SubTotal, 0) as SubTotal,
        10.0 as VatRate, -- Default VAT rate
        ISNULL(order_totals.SubTotal, 0) * 0.1 as VatAmount,
        ISNULL(order_totals.SubTotal, 0) * 1.1 as TotalAmount,
        
        -- Additional information
        CAST(NULL AS NVARCHAR(MAX)) as Description,
        CAST(NULL AS NVARCHAR(MAX)) as Notes,
        
        o.voucher_no as VoucherNumber
        
    FROM dbo.[order] o
    LEFT JOIN dbo.customer c ON o.cust_id = c.id
    LEFT JOIN (
        SELECT 
            ord_id,
            SUM(ISNULL(amount, 0)) as SubTotal
        FROM dbo.order_details
        GROUP BY ord_id
    ) order_totals ON o.id = order_totals.ord_id
    WHERE o.id = @order_id;

    -- Return order items information
    SELECT 
        ROW_NUMBER() OVER (ORDER BY od.created) as ItemNo,
        CASE 
            WHEN p.prod_name IS NOT NULL THEN p.prod_name
            WHEN pkg.name IS NOT NULL THEN pkg.name
            ELSE N'Sản phẩm không xác định'
        END as ProductType,
        CASE 
            WHEN p.prod_name IS NOT NULL THEN p.prod_name
            WHEN pkg.name IS NOT NULL THEN pkg.name
            ELSE N'Sản phẩm không xác định'
        END as ProductName,
        CASE 
            WHEN p.description IS NOT NULL THEN p.description
            WHEN pkg.description IS NOT NULL THEN pkg.description
            ELSE N''
        END as ProductDescription,
        1 as Quantity, -- Default quantity
        N'Gói' as Unit, -- Default unit
        ISNULL(od.amount, 0) as UnitPrice,
        ISNULL(od.amount, 0) as TotalPrice,
        CASE 
            WHEN pkg.duration IS NOT NULL THEN pkg.duration
            WHEN od.end_dt IS NOT NULL AND od.start_dt IS NOT NULL 
            THEN DATEDIFF(MONTH, od.start_dt, od.end_dt)
            ELSE 12 -- Default 12 months
        END as Duration,
        CASE 
            WHEN pkg.duration_type IS NOT NULL THEN 
                CASE pkg.duration_type
                    WHEN 1 THEN N'Tháng'
                    WHEN 2 THEN N'Năm'
                    ELSE N'Tháng'
                END
            ELSE N'Tháng'
        END as DurationUnit
        
    FROM dbo.order_details od
    LEFT JOIN dbo.products p ON od.prod_id = p.id
    LEFT JOIN dbo.product_package pkg ON od.package_id = pkg.id
    WHERE od.ord_id = @order_id
    ORDER BY od.created ASC;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_personal_order_detail_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + CAST(@order_id AS NVARCHAR(50));

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET_DETAIL',
                          @SessionID,
                          @AddlInfo;
                          
    -- Re-throw the error
    THROW;
END CATCH;

GO
