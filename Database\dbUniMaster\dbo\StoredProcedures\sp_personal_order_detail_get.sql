-- =============================================
-- Stored Procedure: sp_personal_order_detail_get
-- Description: Get detailed order information including customer details and order items
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_personal_order_detail_get]
    @order_id UNIQUEIDENTIFIER
AS
BEGIN TRY
    SET NOCOUNT ON;

    -- Validate input parameters
    IF @order_id IS NULL
    BEGIN
        RAISERROR('Order ID is required', 16, 1);
        RETURN;
    END

    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @order_id)
    BEGIN
        RAISERROR('Order not found', 16, 1);
        RETURN;
    END

    -- Return order header information with customer details
    SELECT
        o.id as Oid,
        ISNULL(o.ord_name, o.ord_no) as OrderCode,
        ISNULL(o.contract_code, o.ord_no) as ContractCode,
        ISNULL(o.order_date, o.created) as OrderDate,
        o.created as CreatedDate,

        -- Customer information
        c.full_name as CustomerName,
        c.email_1 as CustomerEmail,
        c.phone_1 as CustomerPhone,

        -- Order status (enhanced)
        o.ord_status as Status,
        CASE o.ord_status
            WHEN 0 THEN N'Nháp'
            WHEN 1 THEN N'Chờ thanh toán'
            WHEN 2 THEN N'Đã thanh toán'
            WHEN 3 THEN N'Đang xử lý'
            WHEN 4 THEN N'Hoàn thành'
            WHEN 5 THEN N'Đã hủy'
            WHEN 6 THEN N'Đã hoàn tiền'
            ELSE N'Không xác định'
        END as StatusText,

        -- Financial information (use enhanced fields if available, fallback to calculated)
        ISNULL(o.sub_total, ISNULL(order_totals.SubTotal, 0)) as SubTotal,
        ISNULL(o.vat_rate, 10.0) as VatRate,
        ISNULL(o.vat_amount, ISNULL(order_totals.SubTotal, 0) * (ISNULL(o.vat_rate, 10.0) / 100)) as VatAmount,
        ISNULL(o.total_amount, ISNULL(order_totals.SubTotal, 0) * (1 + ISNULL(o.vat_rate, 10.0) / 100)) as TotalAmount,

        -- Additional information
        o.description as Description,
        o.notes as Notes,
        o.voucher_no as VoucherNumber,
        o.payment_method as PaymentMethod,
        o.payment_date as PaymentDate,
        o.payment_due_date as PaymentDueDate

    FROM dbo.[order] o
    LEFT JOIN dbo.customer c ON o.cust_id = c.id
    LEFT JOIN (
        SELECT
            ord_id,
            SUM(ISNULL(ISNULL(total_price, amount), 0)) as SubTotal
        FROM dbo.order_details
        WHERE ISNULL(is_active, 1) = 1
        GROUP BY ord_id
    ) order_totals ON o.id = order_totals.ord_id
    WHERE o.id = @order_id;

    -- Return order items information (enhanced)
    SELECT
        ISNULL(od.item_no, ROW_NUMBER() OVER (ORDER BY od.created)) as ItemNo,

        -- Product information (use enhanced fields if available, fallback to joined data)
        ISNULL(od.product_type,
            CASE
                WHEN p.prod_name IS NOT NULL THEN p.prod_name
                WHEN pkg.name IS NOT NULL THEN pkg.name
                ELSE N'Sản phẩm không xác định'
            END
        ) as ProductType,

        ISNULL(od.product_name,
            CASE
                WHEN p.prod_name IS NOT NULL THEN p.prod_name
                WHEN pkg.name IS NOT NULL THEN pkg.name
                ELSE N'Sản phẩm không xác định'
            END
        ) as ProductName,

        ISNULL(od.product_description,
            CASE
                WHEN p.description IS NOT NULL THEN p.description
                WHEN pkg.description IS NOT NULL THEN pkg.description
                ELSE N''
            END
        ) as ProductDescription,

        -- Quantity and pricing (use enhanced fields)
        ISNULL(od.quantity, 1) as Quantity,
        ISNULL(od.unit, N'Gói') as Unit,
        ISNULL(od.unit_price, ISNULL(od.amount, 0)) as UnitPrice,
        ISNULL(od.total_price, ISNULL(od.amount, 0)) as TotalPrice,

        -- Duration information (use enhanced fields if available)
        ISNULL(od.duration,
            CASE
                WHEN pkg.duration IS NOT NULL THEN pkg.duration
                WHEN od.end_dt IS NOT NULL AND od.start_dt IS NOT NULL
                THEN DATEDIFF(MONTH, od.start_dt, od.end_dt)
                ELSE 1 -- Default 1 year
            END
        ) as Duration,

        ISNULL(od.duration_unit,
            CASE
                WHEN pkg.duration_type IS NOT NULL THEN
                    CASE pkg.duration_type
                        WHEN 1 THEN N'Tháng'
                        WHEN 2 THEN N'Năm'
                        ELSE N'Năm'
                    END
                ELSE N'năm'
            END
        ) as DurationUnit,

        -- Additional information
        od.start_dt as StartDate,
        od.end_dt as EndDate,
        od.type as RegistrationType,
        CASE od.type
            WHEN 1 THEN N'Đăng ký mới'
            WHEN 2 THEN N'Gia hạn'
            WHEN 3 THEN N'Nâng cấp'
            WHEN 4 THEN N'Hạ cấp'
            ELSE N'Đăng ký mới'
        END as RegistrationTypeText

    FROM dbo.order_details od
    LEFT JOIN dbo.products p ON od.prod_id = p.id
    LEFT JOIN dbo.product_package pkg ON od.package_id = pkg.id
    WHERE od.ord_id = @order_id
        AND ISNULL(od.is_active, 1) = 1
    ORDER BY ISNULL(od.item_no, od.created) ASC;

END TRY
BEGIN CATCH
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_personal_order_detail_get ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + CAST(@order_id AS NVARCHAR(50));

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'GET_DETAIL',
                          @SessionID,
                          @AddlInfo;
                          
    -- Re-throw the error
    THROW;
END CATCH;

GO
