-- =============================================
-- Stored Procedure: sp_order_info_set
-- Description: Create or update order information
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_order_info_set]
    @id UNIQUEIDENTIFIER = NULL,
    @cust_id UNIQUEIDENTIFIER,
    @ord_name NVARCHAR(50),
    @ord_no NVARCHAR(50) = NULL,
    @ord_status BIT = 0,
    @ord_status_pay BIT = 0,
    @voucher_no NVARCHAR(50) = NULL,
    @jump INT = 1,
    @userId UNIQUEIDENTIFIER = NULL,
    @result_id UNIQUEIDENTIFIER = NULL OUTPUT,
    @result_message NVARCHAR(500) = NULL OUTPUT
AS
BEGIN TRY
    SET NOCOUNT ON;
    
    DECLARE @IsUpdate BIT = 0;
    DECLARE @CurrentDate DATETIME = GETDATE();
    
    -- Validate required parameters
    IF @cust_id IS NULL
    BEGIN
        SET @result_message = N'Customer ID is required';
        RAISERROR(@result_message, 16, 1);
        RETURN;
    END
    
    IF @ord_name IS NULL OR LTRIM(RTRIM(@ord_name)) = ''
    BEGIN
        SET @result_message = N'Order name is required';
        RAISERROR(@result_message, 16, 1);
        RETURN;
    END
    
    -- Check if customer exists
    IF NOT EXISTS(SELECT 1 FROM dbo.customer WHERE id = @cust_id)
    BEGIN
        SET @result_message = N'Customer not found';
        RAISERROR(@result_message, 16, 1);
        RETURN;
    END
    
    -- Check if this is an update operation
    IF @id IS NOT NULL AND EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @id)
    BEGIN
        SET @IsUpdate = 1;
        SET @result_id = @id;
    END
    ELSE
    BEGIN
        SET @result_id = NEWID();
        SET @IsUpdate = 0;
    END
    
    -- Generate order number if not provided
    IF @ord_no IS NULL OR LTRIM(RTRIM(@ord_no)) = ''
    BEGIN
        DECLARE @OrderCount INT;
        SELECT @OrderCount = COUNT(*) + 1 FROM dbo.[order] WHERE YEAR(created) = YEAR(@CurrentDate);
        SET @ord_no = 'ORD' + FORMAT(@CurrentDate, 'yyyyMMdd') + FORMAT(@OrderCount, '0000');
    END
    
    -- Check for duplicate order number (excluding current record if updating)
    IF EXISTS(SELECT 1 FROM dbo.[order] WHERE ord_no = @ord_no AND (@IsUpdate = 0 OR id != @result_id))
    BEGIN
        SET @result_message = N'Order number already exists: ' + @ord_no;
        RAISERROR(@result_message, 16, 1);
        RETURN;
    END

    BEGIN TRANSACTION;
    
    IF @IsUpdate = 1
    BEGIN
        -- Update existing order
        UPDATE dbo.[order]
        SET 
            cust_id = @cust_id,
            ord_name = @ord_name,
            ord_no = @ord_no,
            ord_status = @ord_status,
            ord_status_pay = @ord_status_pay,
            voucher_no = @voucher_no,
            jump = @jump,
            updated = @CurrentDate,
            updated_by = @userId
        WHERE id = @result_id;
        
        SET @result_message = N'Order updated successfully';
    END
    ELSE
    BEGIN
        -- Insert new order
        INSERT INTO dbo.[order] (
            id,
            cust_id,
            ord_name,
            ord_no,
            ord_status,
            ord_status_pay,
            voucher_no,
            jump,
            created,
            created_by,
            updated,
            updated_by
        )
        VALUES (
            @result_id,
            @cust_id,
            @ord_name,
            @ord_no,
            @ord_status,
            @ord_status_pay,
            @voucher_no,
            @jump,
            @CurrentDate,
            @userId,
            @CurrentDate,
            @userId
        );
        
        SET @result_message = N'Order created successfully';
    END
    
    COMMIT TRANSACTION;
    
    -- Return success result
    SELECT 
        1 as valid,
        @result_message as message,
        @result_id as data;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
        
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_info_set ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + ISNULL(CAST(@result_id AS NVARCHAR(50)), 'NULL') + 
                   ', CustomerId: ' + ISNULL(CAST(@cust_id AS NVARCHAR(50)), 'NULL');

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'SET',
                          @SessionID,
                          @AddlInfo;
    
    -- Return error result
    SELECT 
        0 as valid,
        @ErrorMsg as message,
        NULL as data;
        
END CATCH;

GO
