-- =============================================
-- Stored Procedure: sp_order_info_delete
-- Description: Delete order and related order details
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_order_info_delete]
    @order_id UNIQUEIDENTIFIER,
    @userId UNIQUEIDENTIFIER = NULL,
    @force_delete BIT = 0
AS
BEGIN TRY
    SET NOCOUNT ON;
    
    -- Validate input parameters
    IF @order_id IS NULL
    BEGIN
        RAISERROR('Order ID is required', 16, 1);
        RETURN;
    END
    
    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @order_id)
    BEGIN
        RAISERROR('Order not found', 16, 1);
        RETURN;
    END
    
    -- Check order status before deletion (business rules)
    DECLARE @ord_status BIT, @ord_status_pay BIT;
    SELECT @ord_status = ord_status, @ord_status_pay = ord_status_pay 
    FROM dbo.[order] 
    WHERE id = @order_id;
    
    -- Prevent deletion of completed/paid orders unless forced
    IF @force_delete = 0 AND (@ord_status = 1 OR @ord_status_pay = 1)
    BEGIN
        RAISERROR('Cannot delete completed or paid orders. Use force_delete = 1 if necessary.', 16, 1);
        RETURN;
    END
    
    -- Check for related contracts
    DECLARE @ContractCount INT;
    SELECT @ContractCount = COUNT(*)
    FROM dbo.contract c
    INNER JOIN dbo.order_details od ON c.ord_dts_id = od.id
    WHERE od.ord_id = @order_id;
    
    IF @ContractCount > 0 AND @force_delete = 0
    BEGIN
        RAISERROR('Cannot delete order with existing contracts. Use force_delete = 1 if necessary.', 16, 1);
        RETURN;
    END

    BEGIN TRANSACTION;
    
    -- If force delete, remove related contracts first
    IF @force_delete = 1 AND @ContractCount > 0
    BEGIN
        DELETE c
        FROM dbo.contract c
        INNER JOIN dbo.order_details od ON c.ord_dts_id = od.id
        WHERE od.ord_id = @order_id;
    END
    
    -- Delete order details first (due to foreign key constraint)
    DELETE FROM dbo.order_details 
    WHERE ord_id = @order_id;
    
    -- Delete the order
    DELETE FROM dbo.[order] 
    WHERE id = @order_id;
    
    COMMIT TRANSACTION;
    
    -- Return success result
    SELECT 
        1 as valid,
        N'Order deleted successfully' as message;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
        
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_info_delete ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + CAST(@order_id AS NVARCHAR(50)) + 
                   ', ForceDelete: ' + CAST(@force_delete AS NVARCHAR(1));

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'DELETE',
                          @SessionID,
                          @AddlInfo;
    
    -- Return error result
    SELECT 
        0 as valid,
        @ErrorMsg as message;
        
END CATCH;

GO
