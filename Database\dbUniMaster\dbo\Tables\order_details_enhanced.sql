-- =============================================
-- Enhanced Order Details Table Structure
-- Description: Modified order_details table to support the order details UI functionality
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

-- Drop existing constraints and table if needed for modification
-- Note: Run this carefully in production - backup data first!

/*
-- Backup existing data
SELECT * INTO order_details_backup FROM [dbo].[order_details];

-- Drop existing table
DROP TABLE [dbo].[order_details];
*/

-- Create enhanced order_details table
CREATE TABLE [dbo].[order_details] (
    [id]                UNIQUEIDENTIFIER CONSTRAINT [DF_order_details_id] DEFAULT (newid()) NOT NULL,
    [ord_id]            UNIQUEIDENTIFIER NULL,
    [prod_id]           UNIQUEIDENTIFIER NULL,
    [package_id]        UNIQUEIDENTIFIER NULL,
    
    -- Item information (as shown in UI)
    [item_no]           INT              NULL, -- STT (Sequential number)
    [product_name]      NVARCHAR(200)    NULL, -- Sản phẩm name
    [product_type]      NVARCHAR(100)    NULL, -- Product type/category
    [product_description] NVARCHAR(MAX)  NULL, -- Product description
    
    -- Quantity and pricing (as shown in UI)
    [quantity]          INT              CONSTRAINT [DF_order_details_quantity] DEFAULT (1) NULL,
    [unit]              NVARCHAR(20)     CONSTRAINT [DF_order_details_unit] DEFAULT (N'Gói') NULL,
    [unit_price]        DECIMAL(18,2)    CONSTRAINT [DF_order_details_unit_price] DEFAULT (0) NULL,
    [total_price]       DECIMAL(18,2)    CONSTRAINT [DF_order_details_total_price] DEFAULT (0) NULL,
    [discount_amount]   DECIMAL(18,2)    CONSTRAINT [DF_order_details_discount] DEFAULT (0) NULL,
    
    -- Service duration (Thời hạn as shown in UI)
    [duration]          INT              NULL, -- Duration number
    [duration_unit]     NVARCHAR(20)     CONSTRAINT [DF_order_details_duration_unit] DEFAULT (N'năm') NULL, -- năm, tháng, ngày
    [start_dt]          DATETIME         NULL, -- Service start date
    [end_dt]            DATETIME         NULL, -- Service end date
    
    -- Registration type
    [type]              INT              CONSTRAINT [DF_order_details_type] DEFAULT (1) NULL,
    -- 1: Đăng ký mới, 2: Gia hạn, 3: Nâng cấp, 4: Hạ cấp
    
    -- Legacy amount field (for backward compatibility)
    [amount]            DECIMAL(18,2)    NULL,
    
    -- Additional information
    [notes]             NVARCHAR(MAX)    NULL,
    [is_active]         BIT              CONSTRAINT [DF_order_details_active] DEFAULT (1) NULL,
    
    -- Audit fields
    [created]           DATETIME         CONSTRAINT [DF_order_details_created] DEFAULT (getdate()) NULL,
    [created_by]        UNIQUEIDENTIFIER NULL,
    [updated]           DATETIME         NULL,
    [updated_by]        UNIQUEIDENTIFIER NULL,
    
    CONSTRAINT [PK_order_details] PRIMARY KEY CLUSTERED ([id] ASC),
    CONSTRAINT [FK_order_details_order] FOREIGN KEY ([ord_id]) REFERENCES [dbo].[order] ([id]) ON DELETE CASCADE,
    CONSTRAINT [FK_order_details_products] FOREIGN KEY ([prod_id]) REFERENCES [dbo].[products] ([id]),
    CONSTRAINT [FK_order_details_package] FOREIGN KEY ([package_id]) REFERENCES [dbo].[product_package] ([id])
);

-- Create indexes for better performance
CREATE INDEX [IX_order_details_ord_id] ON [dbo].[order_details] ([ord_id]);
CREATE INDEX [IX_order_details_prod_id] ON [dbo].[order_details] ([prod_id]);
CREATE INDEX [IX_order_details_package_id] ON [dbo].[order_details] ([package_id]);
CREATE INDEX [IX_order_details_item_no] ON [dbo].[order_details] ([ord_id], [item_no]);
CREATE INDEX [IX_order_details_type] ON [dbo].[order_details] ([type]);
CREATE INDEX [IX_order_details_active] ON [dbo].[order_details] ([is_active]);

-- Add extended properties for documentation
EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Số thứ tự item trong đơn hàng', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'item_no';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Tên sản phẩm/dịch vụ', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'product_name';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Số lượng', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'quantity';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Đơn vị tính (gói, tháng, năm, ...)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'unit';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Đơn giá', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'unit_price';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Thành tiền', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'total_price';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Thời gian sử dụng dịch vụ', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'duration';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'Đơn vị thời gian (năm, tháng, ngày)', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'duration_unit';

EXECUTE sp_addextendedproperty @name = N'MS_Description', 
    @value = N'1: Đăng ký mới, 2: Gia hạn, 3: Nâng cấp, 4: Hạ cấp', 
    @level0type = N'SCHEMA', @level0name = N'dbo', @level1type = N'TABLE', @level1name = N'order_details', 
    @level2type = N'COLUMN', @level2name = N'type';

GO

-- Create trigger to automatically calculate total_price when unit_price or quantity changes
CREATE OR ALTER TRIGGER [dbo].[tr_order_details_calculate_total]
ON [dbo].[order_details]
AFTER INSERT, UPDATE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Update total_price based on quantity * unit_price - discount_amount
    UPDATE od
    SET total_price = (ISNULL(od.quantity, 1) * ISNULL(od.unit_price, 0)) - ISNULL(od.discount_amount, 0),
        amount = (ISNULL(od.quantity, 1) * ISNULL(od.unit_price, 0)) - ISNULL(od.discount_amount, 0) -- Keep legacy field in sync
    FROM [dbo].[order_details] od
    INNER JOIN inserted i ON od.id = i.id;
    
    -- Update order totals
    UPDATE o
    SET sub_total = order_totals.sub_total,
        vat_amount = order_totals.sub_total * (ISNULL(o.vat_rate, 10) / 100),
        total_amount = order_totals.sub_total * (1 + ISNULL(o.vat_rate, 10) / 100) - ISNULL(o.discount_amount, 0)
    FROM [dbo].[order] o
    INNER JOIN (
        SELECT 
            od.ord_id,
            SUM(ISNULL(od.total_price, 0)) as sub_total
        FROM [dbo].[order_details] od
        INNER JOIN inserted i ON od.ord_id = i.ord_id
        WHERE od.is_active = 1
        GROUP BY od.ord_id
    ) order_totals ON o.id = order_totals.ord_id;
END;

GO
