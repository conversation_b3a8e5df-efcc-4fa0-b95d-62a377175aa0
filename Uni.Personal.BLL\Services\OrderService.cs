using System;
using System.Threading.Tasks;
using UNI.Common.CommonBase;
using UNI.Model;
using Uni.Personal.BLL.Interfaces;
using Uni.Personal.DAL.Interfaces;
using Uni.Personal.Model;

namespace Uni.Personal.BLL.Services
{
    /// <summary>
    /// Order service implementation
    /// </summary>
    public class OrderService : UniBaseService, IOrderService
    {
        private readonly IOrderRepository _orderRepository;

        public OrderService(IOrderRepository orderRepository)
        {
            _orderRepository = orderRepository;
        }

        /// <summary>
        /// Get paginated list of orders
        /// </summary>
        /// <param name="query">Filter input for orders</param>
        /// <returns>Paginated list of orders</returns>
        public async Task<CommonListPage> GetPageAsync(OrderFilterInput? query)
        {
            return await _orderRepository.GetPageAsync(query);
        }

        /// <summary>
        /// Get order information by ID
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Order information</returns>
        public async Task<CommonViewOidInfo> GetInfoAsync(Guid? oid)
        {
            return await _orderRepository.GetInfoAsync(oid);
        }

        /// <summary>
        /// Create or update order
        /// </summary>
        /// <param name="info">Order information</param>
        /// <returns>Validation result with order ID</returns>
        public async Task<BaseValidate<Guid?>> SetInfoAsync(CommonViewOidInfo? info)
        {
            // Add business logic validation here if needed
            // For example: validate order data, check business rules, etc.
            
            return await _orderRepository.SetInfoAsync(info);
        }

        /// <summary>
        /// Delete order
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> DeleteAsync(Guid? oid)
        {
            // Add business logic validation here if needed
            // For example: check if order can be deleted, validate permissions, etc.

            return await _orderRepository.DeleteAsync(oid);
        }

        /// <summary>
        /// Get detailed order information including items
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <returns>Detailed order information</returns>
        public async Task<OrderDetailView?> GetOrderDetailAsync(Guid? oid)
        {
            return await _orderRepository.GetOrderDetailAsync(oid);
        }

        /// <summary>
        /// Update order status
        /// </summary>
        /// <param name="oid">Order ID</param>
        /// <param name="status">New status</param>
        /// <returns>Validation result</returns>
        public async Task<BaseValidate> UpdateOrderStatusAsync(Guid? oid, OrderStatus status)
        {
            // Add business logic validation here if needed
            // For example: validate status transitions, check permissions, etc.

            return await _orderRepository.UpdateOrderStatusAsync(oid, (int)status);
        }
    }
}
