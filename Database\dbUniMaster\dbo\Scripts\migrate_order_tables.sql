-- =============================================
-- Migration Script for Order Tables Enhancement
-- Description: Safely migrate existing order and order_details tables to enhanced structure
-- Author: System Generated
-- Create Date: 2025-01-08
-- WARNING: Test this script in a development environment first!
-- =============================================

-- Step 1: Backup existing data
PRINT 'Creating backup tables...';

-- Backup order table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[order_backup]') AND type in (N'U'))
BEGIN
    SELECT * INTO [dbo].[order_backup] FROM [dbo].[order];
    PRINT 'Order table backed up to order_backup';
END

-- Backup order_details table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[order_details_backup]') AND type in (N'U'))
BEGIN
    SELECT * INTO [dbo].[order_details_backup] FROM [dbo].[order_details];
    PRINT 'Order_details table backed up to order_details_backup';
END

-- Step 2: Add new columns to existing order table
PRINT 'Adding new columns to order table...';

-- Add new columns to order table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'contract_code')
    ALTER TABLE [dbo].[order] ADD [contract_code] NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'sub_total')
    ALTER TABLE [dbo].[order] ADD [sub_total] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'vat_rate')
    ALTER TABLE [dbo].[order] ADD [vat_rate] DECIMAL(5,2) DEFAULT (10) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'vat_amount')
    ALTER TABLE [dbo].[order] ADD [vat_amount] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'total_amount')
    ALTER TABLE [dbo].[order] ADD [total_amount] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'discount_amount')
    ALTER TABLE [dbo].[order] ADD [discount_amount] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'order_date')
    ALTER TABLE [dbo].[order] ADD [order_date] DATETIME NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'payment_due_date')
    ALTER TABLE [dbo].[order] ADD [payment_due_date] DATETIME NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'payment_date')
    ALTER TABLE [dbo].[order] ADD [payment_date] DATETIME NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'description')
    ALTER TABLE [dbo].[order] ADD [description] NVARCHAR(MAX) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'notes')
    ALTER TABLE [dbo].[order] ADD [notes] NVARCHAR(MAX) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'payment_method')
    ALTER TABLE [dbo].[order] ADD [payment_method] NVARCHAR(50) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'payment_reference')
    ALTER TABLE [dbo].[order] ADD [payment_reference] NVARCHAR(100) NULL;

-- Step 3: Modify existing columns in order table
PRINT 'Modifying existing columns in order table...';

-- Change ord_status from BIT to INT
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'ord_status' AND system_type_id = 104) -- BIT type
BEGIN
    -- Add temporary column
    ALTER TABLE [dbo].[order] ADD [ord_status_temp] INT NULL;
    
    -- Copy data with conversion
    UPDATE [dbo].[order] 
    SET [ord_status_temp] = CASE 
        WHEN [ord_status] = 1 THEN 4 -- Completed
        WHEN [ord_status] = 0 THEN 1 -- Waiting for payment
        ELSE 0 -- Draft
    END;
    
    -- Drop old column and rename new one
    ALTER TABLE [dbo].[order] DROP COLUMN [ord_status];
    EXEC sp_rename '[dbo].[order].[ord_status_temp]', 'ord_status', 'COLUMN';
END

-- Change ord_status_pay from BIT to INT
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'ord_status_pay' AND system_type_id = 104) -- BIT type
BEGIN
    -- Add temporary column
    ALTER TABLE [dbo].[order] ADD [ord_status_pay_temp] INT NULL;
    
    -- Copy data with conversion
    UPDATE [dbo].[order] 
    SET [ord_status_pay_temp] = CASE 
        WHEN [ord_status_pay] = 1 THEN 1 -- Paid
        ELSE 0 -- Unpaid
    END;
    
    -- Drop old column and rename new one
    ALTER TABLE [dbo].[order] DROP COLUMN [ord_status_pay];
    EXEC sp_rename '[dbo].[order].[ord_status_pay_temp]', 'ord_status_pay', 'COLUMN';
END

-- Increase ord_name size
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = 'ord_name' AND max_length = 100) -- 50 chars = 100 bytes for NVARCHAR
BEGIN
    ALTER TABLE [dbo].[order] ALTER COLUMN [ord_name] NVARCHAR(100) NULL;
END

-- Step 4: Add new columns to order_details table
PRINT 'Adding new columns to order_details table...';

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'item_no')
    ALTER TABLE [dbo].[order_details] ADD [item_no] INT NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'product_name')
    ALTER TABLE [dbo].[order_details] ADD [product_name] NVARCHAR(200) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'product_type')
    ALTER TABLE [dbo].[order_details] ADD [product_type] NVARCHAR(100) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'product_description')
    ALTER TABLE [dbo].[order_details] ADD [product_description] NVARCHAR(MAX) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'quantity')
    ALTER TABLE [dbo].[order_details] ADD [quantity] INT DEFAULT (1) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'unit')
    ALTER TABLE [dbo].[order_details] ADD [unit] NVARCHAR(20) DEFAULT (N'Gói') NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'unit_price')
    ALTER TABLE [dbo].[order_details] ADD [unit_price] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'total_price')
    ALTER TABLE [dbo].[order_details] ADD [total_price] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'discount_amount')
    ALTER TABLE [dbo].[order_details] ADD [discount_amount] DECIMAL(18,2) DEFAULT (0) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'duration')
    ALTER TABLE [dbo].[order_details] ADD [duration] INT NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'duration_unit')
    ALTER TABLE [dbo].[order_details] ADD [duration_unit] NVARCHAR(20) DEFAULT (N'năm') NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'notes')
    ALTER TABLE [dbo].[order_details] ADD [notes] NVARCHAR(MAX) NULL;

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = 'is_active')
    ALTER TABLE [dbo].[order_details] ADD [is_active] BIT DEFAULT (1) NULL;

-- Step 5: Populate new fields with data from existing fields and related tables
PRINT 'Populating new fields with existing data...';

-- Update order table
UPDATE o
SET 
    contract_code = o.ord_no,
    order_date = ISNULL(o.created, GETDATE()),
    sub_total = ISNULL(order_totals.total_amount, 0),
    vat_rate = 10.0,
    vat_amount = ISNULL(order_totals.total_amount, 0) * 0.1,
    total_amount = ISNULL(order_totals.total_amount, 0) * 1.1
FROM [dbo].[order] o
LEFT JOIN (
    SELECT 
        ord_id,
        SUM(ISNULL(amount, 0)) as total_amount
    FROM [dbo].[order_details]
    GROUP BY ord_id
) order_totals ON o.id = order_totals.ord_id;

-- Update order_details table
UPDATE od
SET 
    item_no = row_nums.row_num,
    product_name = ISNULL(p.prod_name, pkg.name),
    product_type = ISNULL(p.prod_name, pkg.name),
    product_description = ISNULL(p.description, pkg.description),
    quantity = 1,
    unit = N'Gói',
    unit_price = ISNULL(od.amount, 0),
    total_price = ISNULL(od.amount, 0),
    duration = ISNULL(pkg.duration, 
        CASE 
            WHEN od.end_dt IS NOT NULL AND od.start_dt IS NOT NULL 
            THEN DATEDIFF(MONTH, od.start_dt, od.end_dt)
            ELSE 12
        END),
    duration_unit = CASE ISNULL(pkg.duration_type, 2)
        WHEN 1 THEN N'Tháng'
        WHEN 2 THEN N'Năm'
        ELSE N'Năm'
    END,
    is_active = 1
FROM [dbo].[order_details] od
LEFT JOIN [dbo].[products] p ON od.prod_id = p.id
LEFT JOIN [dbo].[product_package] pkg ON od.package_id = pkg.id
LEFT JOIN (
    SELECT 
        id,
        ROW_NUMBER() OVER (PARTITION BY ord_id ORDER BY created) as row_num
    FROM [dbo].[order_details]
) row_nums ON od.id = row_nums.id;

-- Step 6: Create indexes
PRINT 'Creating indexes...';

-- Order table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = N'IX_order_contract_code')
    CREATE INDEX [IX_order_contract_code] ON [dbo].[order] ([contract_code]);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[order]') AND name = N'IX_order_order_date')
    CREATE INDEX [IX_order_order_date] ON [dbo].[order] ([order_date]);

-- Order_details table indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = N'IX_order_details_item_no')
    CREATE INDEX [IX_order_details_item_no] ON [dbo].[order_details] ([ord_id], [item_no]);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[order_details]') AND name = N'IX_order_details_active')
    CREATE INDEX [IX_order_details_active] ON [dbo].[order_details] ([is_active]);

PRINT 'Migration completed successfully!';
PRINT 'Please test the enhanced stored procedure: sp_personal_order_detail_get';

GO
