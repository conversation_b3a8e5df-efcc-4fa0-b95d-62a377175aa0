-- =============================================
-- Stored Procedure: sp_order_status_update
-- Description: Update order status and payment status
-- Author: System Generated
-- Create Date: 2025-01-08
-- =============================================

CREATE OR ALTER PROCEDURE [dbo].[sp_order_status_update]
    @order_id UNIQUEIDENTIFIER,
    @ord_status BIT = NULL,
    @ord_status_pay BIT = NULL,
    @userId UNIQUEIDENTIFIER = NULL,
    @notes NVARCHAR(500) = NULL
AS
BEGIN TRY
    SET NOCOUNT ON;
    
    -- Validate input parameters
    IF @order_id IS NULL
    BEGIN
        RAISERROR('Order ID is required', 16, 1);
        RETURN;
    END
    
    -- At least one status must be provided
    IF @ord_status IS NULL AND @ord_status_pay IS NULL
    BEGIN
        RAISERROR('At least one status (ord_status or ord_status_pay) must be provided', 16, 1);
        RETURN;
    END
    
    -- Check if order exists
    IF NOT EXISTS(SELECT 1 FROM dbo.[order] WHERE id = @order_id)
    BEGIN
        RAISERROR('Order not found', 16, 1);
        RETURN;
    END
    
    -- Get current status for validation and logging
    DECLARE @current_ord_status BIT, @current_ord_status_pay BIT;
    SELECT 
        @current_ord_status = ord_status,
        @current_ord_status_pay = ord_status_pay
    FROM dbo.[order] 
    WHERE id = @order_id;
    
    -- Business rule validations
    -- Cannot change payment status to unpaid if order is completed
    IF @ord_status_pay = 0 AND @current_ord_status = 1
    BEGIN
        RAISERROR('Cannot change payment status to unpaid for completed orders', 16, 1);
        RETURN;
    END
    
    -- Cannot complete order if payment is not made
    IF @ord_status = 1 AND ISNULL(@ord_status_pay, @current_ord_status_pay) = 0
    BEGIN
        RAISERROR('Cannot complete order without payment', 16, 1);
        RETURN;
    END

    BEGIN TRANSACTION;
    
    -- Update order status
    UPDATE dbo.[order]
    SET 
        ord_status = ISNULL(@ord_status, ord_status),
        ord_status_pay = ISNULL(@ord_status_pay, ord_status_pay),
        updated = GETDATE(),
        updated_by = @userId
    WHERE id = @order_id;
    
    -- Log status change if needed (optional - you can create an audit table)
    -- INSERT INTO order_status_log (order_id, old_status, new_status, old_pay_status, new_pay_status, changed_by, changed_date, notes)
    -- VALUES (@order_id, @current_ord_status, ISNULL(@ord_status, @current_ord_status), 
    --         @current_ord_status_pay, ISNULL(@ord_status_pay, @current_ord_status_pay), 
    --         @userId, GETDATE(), @notes);
    
    COMMIT TRANSACTION;
    
    -- Get updated status text for response
    DECLARE @ord_status_text NVARCHAR(100), @pay_status_text NVARCHAR(100);
    
    SELECT @ord_status_text = cd1.value1
    FROM dbo.sys_config_data cd1 
    WHERE cd1.key_1 = 'ord_status' AND cd1.key_2 = ISNULL(@ord_status, @current_ord_status);
    
    SELECT @pay_status_text = cd2.value1
    FROM dbo.sys_config_data cd2 
    WHERE cd2.key_1 = 'ord_status_pay' AND cd2.key_2 = ISNULL(@ord_status_pay, @current_ord_status_pay);
    
    -- Return success result
    SELECT 
        1 as valid,
        N'Order status updated successfully' as message,
        @order_id as order_id,
        ISNULL(@ord_status, @current_ord_status) as ord_status,
        ISNULL(@ord_status_text, N'Unknown') as ord_status_text,
        ISNULL(@ord_status_pay, @current_ord_status_pay) as ord_status_pay,
        ISNULL(@pay_status_text, N'Unknown') as pay_status_text;

END TRY
BEGIN CATCH
    IF @@TRANCOUNT > 0
        ROLLBACK TRANSACTION;
        
    DECLARE @ErrorNum INT,
            @ErrorMsg VARCHAR(500),
            @ErrorProc VARCHAR(50),
            @SessionID INT,
            @AddlInfo VARCHAR(MAX);

    SET @ErrorNum = ERROR_NUMBER();
    SET @ErrorMsg = 'sp_order_status_update ' + ERROR_MESSAGE();
    SET @ErrorProc = ERROR_PROCEDURE();
    SET @AddlInfo = 'OrderId: ' + CAST(@order_id AS NVARCHAR(50)) + 
                   ', OrdStatus: ' + ISNULL(CAST(@ord_status AS NVARCHAR(1)), 'NULL') +
                   ', PayStatus: ' + ISNULL(CAST(@ord_status_pay AS NVARCHAR(1)), 'NULL');

    EXEC utl_errorlog_set @ErrorNum,
                          @ErrorMsg,
                          @ErrorProc,
                          'order',
                          'UPDATE_STATUS',
                          @SessionID,
                          @AddlInfo;
    
    -- Return error result
    SELECT 
        0 as valid,
        @ErrorMsg as message;
        
END CATCH;

GO
